import { getCurrent<PERSON>ser<PERSON><PERSON> } from '@/backend/api';
import { UnauthorizedError } from '@/backend/errors';
import { createErrorContext, errorLogger } from '@/lib/error-handling';
import { NextResponse } from 'next/server';

export async function GET() {
	try {
		const user = await getCurrentUserApi();

		return NextResponse.json(user);
	} catch (error) {
		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		// Enhanced error logging
		errorLogger.error(
			'Failed to get current user',
			error instanceof Error ? error : new Error(String(error)),
			createErrorContext('UserAPI', 'get_current_user'),
			'UserAPI'
		);

		return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
	}
}
