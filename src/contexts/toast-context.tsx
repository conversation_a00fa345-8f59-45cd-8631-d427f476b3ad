'use client';

import { AppError, ErrorSeverity } from '@/lib/error-handling';
import { createContext, useContext, ReactNode } from 'react';
import { toast } from 'sonner';

// ============================================================================
// TYPES
// ============================================================================

export interface ToastErrorOptions {
	onRetry?: () => void | Promise<void>;
	showErrorId?: boolean;
	duration?: number;
}

export interface ToastOptions {
	duration?: number;
}

export interface ToastContextType {
	showError: (error: AppError | Error | string, options?: ToastErrorOptions) => void;
	showSuccess: (message: string, options?: ToastOptions) => void;
	showInfo: (message: string, description?: string, duration?: number) => void;
	showWarning: (message: string, description?: string, duration?: number) => void;
	showLoading: (message: string, description?: string) => string | number;
	showPromise: <T>(
		promise: Promise<T>,
		messages: {
			loading: string;
			success: string | ((data: T) => string);
			error: string | ((error: any) => string);
		},
		options?: {
			loadingDescription?: string;
			successDescription?: string | ((data: T) => string);
			errorDescription?: string | ((error: any) => string);
		}
	) => void;
	dismiss: (toastId?: string | number) => void;
	dismissAll: () => void;
}

// ============================================================================
// CONTEXT
// ============================================================================

const ToastContext = createContext<ToastContextType | undefined>(undefined);

// ============================================================================
// PROVIDER
// ============================================================================

interface ToastProviderProps {
	children: ReactNode;
}

export function ToastProvider({ children }: ToastProviderProps) {
	const showError = (error: AppError | Error | string, options: ToastErrorOptions = {}) => {
		const { onRetry, showErrorId = process.env.NODE_ENV === 'development', duration } = options;

		// Handle string errors
		if (typeof error === 'string') {
			return toast.error(error, {
				duration: duration || 6000,
				action: onRetry
					? {
							label: 'Retry',
							onClick: onRetry,
						}
					: undefined,
			});
		}

		// Handle regular Error objects
		if (!(error instanceof AppError)) {
			return toast.error(error.message || 'An unexpected error occurred', {
				description: showErrorId ? `Error: ${error.name}` : undefined,
				duration: duration || 6000,
				action: onRetry
					? {
							label: 'Retry',
							onClick: onRetry,
						}
					: undefined,
			});
		}

		// Handle AppError objects
		const message = error.message;
		const description = showErrorId ? `Error ID: ${error.id.slice(-8)}` : undefined;
		const action = onRetry
			? {
					label: 'Retry',
					onClick: async () => {
						try {
							await onRetry();
						} catch (retryError) {
							console.error('Retry failed:', retryError);
						}
					},
				}
			: undefined;

		// Map error severity to toast type
		switch (error.severity) {
			case ErrorSeverity.CRITICAL:
			case ErrorSeverity.HIGH:
				return toast.error(message, {
					description,
					action,
					duration: duration || 0, // Don't auto-dismiss critical/high errors
				});

			case ErrorSeverity.MEDIUM:
				return toast.warning(message, {
					description,
					action,
					duration: duration || 8000,
				});

			case ErrorSeverity.LOW:
				return toast.info(message, {
					description,
					action,
					duration: duration || 5000,
				});

			default:
				return toast.error(message, {
					description,
					action,
					duration: duration || 6000,
				});
		}
	};

	const showSuccess = (message: string, options: ToastOptions = {}) => {
		const { duration = 4000 } = options;
		return toast.success(message, { duration });
	};

	const showInfo = (message: string, description?: string, duration = 5000) => {
		return toast.info(message, { description, duration });
	};

	const showWarning = (message: string, description?: string, duration = 6000) => {
		return toast.warning(message, { description, duration });
	};

	const showLoading = (message: string, description?: string) => {
		return toast.loading(message, { description });
	};

	const showPromise = <T,>(
		promise: Promise<T>,
		messages: {
			loading: string;
			success: string | ((data: T) => string);
			error: string | ((error: any) => string);
		},
		options?: {
			loadingDescription?: string;
			successDescription?: string | ((data: T) => string);
			errorDescription?: string | ((error: any) => string);
		}
	) => {
		return toast.promise(promise, {
			loading: messages.loading,
			success: messages.success,
			error: messages.error,
			...options,
		});
	};

	const dismiss = (toastId?: string | number) => {
		return toast.dismiss(toastId);
	};

	const dismissAll = () => {
		return toast.dismiss();
	};

	const value: ToastContextType = {
		showError,
		showSuccess,
		showInfo,
		showWarning,
		showLoading,
		showPromise,
		dismiss,
		dismissAll,
	};

	return <ToastContext.Provider value={value}>{children}</ToastContext.Provider>;
}

// ============================================================================
// HOOK
// ============================================================================

export function useToast(): ToastContextType {
	const context = useContext(ToastContext);
	if (context === undefined) {
		throw new Error('useToast must be used within a ToastProvider');
	}
	return context;
}

// ============================================================================
// EXPORTS
// ============================================================================

export default ToastProvider;
