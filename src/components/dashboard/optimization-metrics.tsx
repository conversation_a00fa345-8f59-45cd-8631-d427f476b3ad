'use client';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { CostAnalysis } from '@/lib/api/token-monitor.api';
import { Award, DollarSign, Target, TrendingDown, TrendingUp } from 'lucide-react';

interface OptimizationMetricsProps {
	analysis: CostAnalysis;
}

export function OptimizationMetrics({ analysis }: OptimizationMetricsProps) {
	// Calculate overall optimization efficiency
	const dailyEfficiency =
		analysis.daily.optimizationSavings.costSaved /
		(analysis.daily.totalCost + analysis.daily.optimizationSavings.costSaved);

	const weeklyEfficiency =
		analysis.weekly.optimizationSavings.costSaved /
		(analysis.weekly.totalCost + analysis.weekly.optimizationSavings.costSaved);

	const monthlyEfficiency =
		analysis.monthly.optimizationSavings.costSaved /
		(analysis.monthly.totalCost + analysis.monthly.optimizationSavings.costSaved);

	// Calculate trends
	const tokenTrend = analysis.trends.tokenUsageTrend;
	const costTrend = analysis.trends.costTrend;
	const optimizationTrend = analysis.trends.optimizationTrend;

	// Get top performing operations
	const operationEfficiency = Object.entries(analysis.byOperation)
		.map(([operation, stats]) => ({
			operation: operation.replace(/([A-Z])/g, ' $1').trim(),
			efficiency:
				stats.optimizationSavings.costSaved /
				(stats.totalCost + stats.optimizationSavings.costSaved),
			tokensSaved: stats.optimizationSavings.tokensSaved,
			costSaved: stats.optimizationSavings.costSaved,
			compressionRatio: stats.optimizationSavings.compressionRatio,
		}))
		.sort((a, b) => b.efficiency - a.efficiency);

	const getEfficiencyColor = (efficiency: number) => {
		if (efficiency >= 0.6) return 'text-green-600';
		if (efficiency >= 0.4) return 'text-yellow-600';
		return 'text-red-600';
	};

	const getEfficiencyBadge = (efficiency: number) => {
		if (efficiency >= 0.6) return 'default';
		if (efficiency >= 0.4) return 'secondary';
		return 'destructive';
	};

	const getTrendIcon = (trend: number) => {
		if (trend > 0) return <TrendingUp className="h-4 w-4 text-red-600" />;
		if (trend < 0) return <TrendingDown className="h-4 w-4 text-green-600" />;
		return <div className="h-4 w-4" />;
	};

	const getTrendColor = (trend: number, inverse = false) => {
		const isGood = inverse ? trend > 0 : trend < 0;
		return isGood ? 'text-green-600' : 'text-red-600';
	};

	return (
		<div className="space-y-4">
			{/* Optimization Overview */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center">
						<Target className="h-5 w-5 mr-2" />
						Optimization Performance Overview
					</CardTitle>
					<CardDescription>
						Comprehensive view of optimization effectiveness across time periods
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
						<div className="space-y-3">
							<div className="flex items-center justify-between">
								<span className="text-sm font-medium">Daily Efficiency</span>
								<Badge variant={getEfficiencyBadge(dailyEfficiency)}>
									{(dailyEfficiency * 100).toFixed(1)}%
								</Badge>
							</div>
							<Progress value={dailyEfficiency * 100} className="h-2" />
							<div className="text-xs text-muted-foreground">
								${analysis.daily.optimizationSavings.costSaved.toFixed(4)} saved
								today
							</div>
						</div>

						<div className="space-y-3">
							<div className="flex items-center justify-between">
								<span className="text-sm font-medium">Weekly Efficiency</span>
								<Badge variant={getEfficiencyBadge(weeklyEfficiency)}>
									{(weeklyEfficiency * 100).toFixed(1)}%
								</Badge>
							</div>
							<Progress value={weeklyEfficiency * 100} className="h-2" />
							<div className="text-xs text-muted-foreground">
								${analysis.weekly.optimizationSavings.costSaved.toFixed(4)} saved
								this week
							</div>
						</div>

						<div className="space-y-3">
							<div className="flex items-center justify-between">
								<span className="text-sm font-medium">Monthly Efficiency</span>
								<Badge variant={getEfficiencyBadge(monthlyEfficiency)}>
									{(monthlyEfficiency * 100).toFixed(1)}%
								</Badge>
							</div>
							<Progress value={monthlyEfficiency * 100} className="h-2" />
							<div className="text-xs text-muted-foreground">
								${analysis.monthly.optimizationSavings.costSaved.toFixed(4)} saved
								this month
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Trend Analysis */}
			<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Token Usage Trend</CardTitle>
						{getTrendIcon(tokenTrend)}
					</CardHeader>
					<CardContent>
						<div className={`text-2xl font-bold ${getTrendColor(tokenTrend)}`}>
							{tokenTrend > 0 ? '+' : ''}
							{tokenTrend.toFixed(1)}%
						</div>
						<p className="text-xs text-muted-foreground">
							{tokenTrend < 0 ? 'Decreased' : 'Increased'} vs previous period
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Cost Trend</CardTitle>
						{getTrendIcon(costTrend)}
					</CardHeader>
					<CardContent>
						<div className={`text-2xl font-bold ${getTrendColor(costTrend)}`}>
							{costTrend > 0 ? '+' : ''}
							{costTrend.toFixed(1)}%
						</div>
						<p className="text-xs text-muted-foreground">
							{costTrend < 0 ? 'Decreased' : 'Increased'} vs previous period
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Optimization Trend</CardTitle>
						{getTrendIcon(-optimizationTrend)}
					</CardHeader>
					<CardContent>
						<div
							className={`text-2xl font-bold ${getTrendColor(
								optimizationTrend,
								true
							)}`}
						>
							{optimizationTrend > 0 ? '+' : ''}
							{optimizationTrend.toFixed(1)}%
						</div>
						<p className="text-xs text-muted-foreground">
							Compression ratio improvement
						</p>
					</CardContent>
				</Card>
			</div>

			{/* Operation Efficiency Ranking */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center">
						<Award className="h-5 w-5 mr-2" />
						Operation Efficiency Ranking
					</CardTitle>
					<CardDescription>
						Performance ranking of different LLM operations by optimization efficiency
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="space-y-3">
						{operationEfficiency.map((op, index) => (
							<div
								key={op.operation}
								className="flex items-center justify-between p-3 border rounded-lg"
							>
								<div className="flex items-center space-x-3">
									<div className="flex items-center justify-center w-6 h-6 rounded-full bg-muted text-xs font-medium">
										{index + 1}
									</div>
									<div>
										<div className="font-medium">{op.operation}</div>
										<div className="text-xs text-muted-foreground">
											{op.tokensSaved.toLocaleString()} tokens saved
										</div>
									</div>
								</div>
								<div className="text-right">
									<div
										className={`text-lg font-bold ${getEfficiencyColor(
											op.efficiency
										)}`}
									>
										{(op.efficiency * 100).toFixed(1)}%
									</div>
									<div className="text-xs text-muted-foreground">
										${op.costSaved.toFixed(4)} saved
									</div>
								</div>
							</div>
						))}
					</div>
				</CardContent>
			</Card>

			{/* Optimization Techniques Performance */}
			<Card>
				<CardHeader>
					<CardTitle>Optimization Techniques Performance</CardTitle>
					<CardDescription>Breakdown of savings by optimization method</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<div className="space-y-4">
							<h4 className="font-medium">Token Optimization</h4>
							<div className="space-y-3">
								<div className="flex justify-between items-center">
									<span className="text-sm">Prompt Compression</span>
									<div className="flex items-center space-x-2">
										<Progress value={65} className="w-20 h-2" />
										<span className="text-sm font-medium">65%</span>
									</div>
								</div>
								<div className="flex justify-between items-center">
									<span className="text-sm">Structured Output</span>
									<div className="flex items-center space-x-2">
										<Progress value={45} className="w-20 h-2" />
										<span className="text-sm font-medium">45%</span>
									</div>
								</div>
								<div className="flex justify-between items-center">
									<span className="text-sm">Template Optimization</span>
									<div className="flex items-center space-x-2">
										<Progress value={55} className="w-20 h-2" />
										<span className="text-sm font-medium">55%</span>
									</div>
								</div>
							</div>
						</div>

						<div className="space-y-4">
							<h4 className="font-medium">System Optimization</h4>
							<div className="space-y-3">
								<div className="flex justify-between items-center">
									<span className="text-sm">Caching</span>
									<div className="flex items-center space-x-2">
										<Progress value={75} className="w-20 h-2" />
										<span className="text-sm font-medium">75%</span>
									</div>
								</div>
								<div className="flex justify-between items-center">
									<span className="text-sm">Batch Processing</span>
									<div className="flex items-center space-x-2">
										<Progress value={80} className="w-20 h-2" />
										<span className="text-sm font-medium">80%</span>
									</div>
								</div>
								<div className="flex justify-between items-center">
									<span className="text-sm">Model Selection</span>
									<div className="flex items-center space-x-2">
										<Progress value={60} className="w-20 h-2" />
										<span className="text-sm font-medium">60%</span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Projected Savings */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center">
						<DollarSign className="h-5 w-5 mr-2" />
						Projected Annual Savings
					</CardTitle>
					<CardDescription>
						Estimated cost savings based on current optimization performance
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
						<div className="text-center p-4 bg-green-50 dark:bg-green-950 rounded-lg">
							<div className="text-2xl font-bold text-green-600">
								${(analysis.monthly.optimizationSavings.costSaved * 12).toFixed(2)}
							</div>
							<div className="text-sm text-green-600">Annual Savings</div>
						</div>
						<div className="text-center p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
							<div className="text-2xl font-bold text-blue-600">
								{(
									(analysis.monthly.optimizationSavings.tokensSaved * 12) /
									1000000
								).toFixed(1)}
								M
							</div>
							<div className="text-sm text-blue-600">Tokens Saved/Year</div>
						</div>
						<div className="text-center p-4 bg-purple-50 dark:bg-purple-950 rounded-lg">
							<div className="text-2xl font-bold text-purple-600">
								{(
									(1 - analysis.monthly.optimizationSavings.compressionRatio) *
									100
								).toFixed(1)}
								%
							</div>
							<div className="text-sm text-purple-600">Avg Compression</div>
						</div>
						<div className="text-center p-4 bg-orange-50 dark:bg-orange-950 rounded-lg">
							<div className="text-2xl font-bold text-orange-600">
								{(monthlyEfficiency * 100).toFixed(1)}%
							</div>
							<div className="text-sm text-orange-600">Overall Efficiency</div>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
