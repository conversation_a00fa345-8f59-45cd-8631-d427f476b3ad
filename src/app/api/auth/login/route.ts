import { usernamePass<PERSON><PERSON><PERSON>in<PERSON><PERSON> } from '@/backend/api';
import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const { username, password } = body;

		await usernamePassword<PERSON>ogin<PERSON><PERSON>(username, password);

		return NextResponse.json({
			success: true,
			message: 'Login successful',
		});
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error('Login error:', error);
		return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
	}
}
