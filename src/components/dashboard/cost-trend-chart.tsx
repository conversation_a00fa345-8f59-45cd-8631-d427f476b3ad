'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CostAnalysis } from '@/lib/api/token-monitor.api';
import {
	CartesianGrid,
	Cell,
	Legend,
	Line,
	LineChart,
	Pie,
	<PERSON><PERSON>,
	ResponsiveContainer,
	Tooltip,
	XAxis,
	YAxis,
} from 'recharts';

interface CostTrendChartProps {
	analysis: CostAnalysis;
}

export function CostTrendChart({ analysis }: CostTrendChartProps) {
	// Prepare trend data
	const trendData = [
		{
			period: 'Daily',
			actualCost: analysis.daily.totalCost,
			savedCost: analysis.daily.optimizationSavings.costSaved,
			totalWithoutOptimization:
				analysis.daily.totalCost + analysis.daily.optimizationSavings.costSaved,
		},
		{
			period: 'Weekly',
			actualCost: analysis.weekly.totalCost,
			savedCost: analysis.weekly.optimizationSavings.costSaved,
			totalWithoutOptimization:
				analysis.weekly.totalCost + analysis.weekly.optimizationSavings.costSaved,
		},
		{
			period: 'Monthly',
			actualCost: analysis.monthly.totalCost,
			savedCost: analysis.monthly.optimizationSavings.costSaved,
			totalWithoutOptimization:
				analysis.monthly.totalCost + analysis.monthly.optimizationSavings.costSaved,
		},
	];

	// Prepare cost breakdown by operation
	const operationCosts = Object.entries(analysis.byOperation)
		.map(([operation, stats]) => ({
			name: operation.replace(/([A-Z])/g, ' $1').trim(),
			value: stats.totalCost,
			savings: stats.optimizationSavings.costSaved,
		}))
		.sort((a, b) => b.value - a.value)
		.slice(0, 6); // Top 6 operations

	// Colors for pie chart
	const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];

	const CustomTooltip = ({ active, payload, label }: any) => {
		if (active && payload && payload.length) {
			return (
				<div className="bg-background border rounded-lg p-3 shadow-lg">
					<p className="font-medium">{label}</p>
					{payload.map((entry: any, index: number) => (
						<p key={index} style={{ color: entry.color }}>
							{entry.name}: ${entry.value.toFixed(4)}
						</p>
					))}
				</div>
			);
		}
		return null;
	};

	const PieTooltip = ({ active, payload }: any) => {
		if (active && payload && payload.length) {
			const data = payload[0].payload;
			return (
				<div className="bg-background border rounded-lg p-3 shadow-lg">
					<p className="font-medium">{data.name}</p>
					<p>Cost: ${data.value.toFixed(4)}</p>
					<p>Saved: ${data.savings.toFixed(4)}</p>
					<p>
						Efficiency:{' '}
						{((data.savings / (data.value + data.savings)) * 100).toFixed(1)}%
					</p>
				</div>
			);
		}
		return null;
	};

	return (
		<div className="space-y-4">
			{/* Cost Trend */}
			<Card>
				<CardHeader>
					<CardTitle>Cost Trends & Savings</CardTitle>
					<CardDescription>
						Actual costs vs potential costs without optimization
					</CardDescription>
				</CardHeader>
				<CardContent>
					<ResponsiveContainer width="100%" height={300}>
						<LineChart
							data={trendData}
							margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
						>
							<CartesianGrid strokeDasharray="3 3" />
							<XAxis dataKey="period" />
							<YAxis tickFormatter={(value) => `$${value.toFixed(3)}`} />
							<Tooltip content={<CustomTooltip />} />
							<Legend />
							<Line
								type="monotone"
								dataKey="totalWithoutOptimization"
								stroke="#ef4444"
								strokeWidth={2}
								name="Cost Without Optimization"
								strokeDasharray="5 5"
							/>
							<Line
								type="monotone"
								dataKey="actualCost"
								stroke="#10b981"
								strokeWidth={3}
								name="Actual Cost"
							/>
							<Line
								type="monotone"
								dataKey="savedCost"
								stroke="#3b82f6"
								strokeWidth={2}
								name="Cost Saved"
							/>
						</LineChart>
					</ResponsiveContainer>
				</CardContent>
			</Card>

			<div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
				{/* Cost Breakdown Pie Chart */}
				<Card>
					<CardHeader>
						<CardTitle>Cost Distribution by Operation</CardTitle>
						<CardDescription>Where your API costs are being spent</CardDescription>
					</CardHeader>
					<CardContent>
						<ResponsiveContainer width="100%" height={300}>
							<PieChart>
								<Pie
									data={operationCosts}
									cx="50%"
									cy="50%"
									labelLine={false}
									label={({ name, percent }) =>
										`${name} ${(percent * 100).toFixed(0)}%`
									}
									outerRadius={80}
									fill="#8884d8"
									dataKey="value"
								>
									{operationCosts.map((entry, index) => (
										<Cell
											key={`cell-${index}`}
											fill={COLORS[index % COLORS.length]}
										/>
									))}
								</Pie>
								<Tooltip content={<PieTooltip />} />
							</PieChart>
						</ResponsiveContainer>
					</CardContent>
				</Card>

				{/* Savings Summary */}
				<Card>
					<CardHeader>
						<CardTitle>Optimization Impact</CardTitle>
						<CardDescription>
							Financial impact of optimization techniques
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="space-y-2">
							<div className="flex justify-between items-center">
								<span className="text-sm font-medium">Daily Savings</span>
								<span className="text-lg font-bold text-green-600">
									${analysis.daily.optimizationSavings.costSaved.toFixed(4)}
								</span>
							</div>
							<div className="flex justify-between items-center">
								<span className="text-sm font-medium">Weekly Savings</span>
								<span className="text-lg font-bold text-green-600">
									${analysis.weekly.optimizationSavings.costSaved.toFixed(4)}
								</span>
							</div>
							<div className="flex justify-between items-center">
								<span className="text-sm font-medium">Monthly Savings</span>
								<span className="text-lg font-bold text-green-600">
									${analysis.monthly.optimizationSavings.costSaved.toFixed(4)}
								</span>
							</div>
						</div>

						<div className="border-t pt-4">
							<div className="flex justify-between items-center">
								<span className="text-sm font-medium">
									Projected Annual Savings
								</span>
								<span className="text-xl font-bold text-green-600">
									$
									{(analysis.monthly.optimizationSavings.costSaved * 12).toFixed(
										2
									)}
								</span>
							</div>
						</div>

						<div className="bg-muted p-3 rounded-lg">
							<div className="text-sm font-medium mb-1">Efficiency Rate</div>
							<div className="text-2xl font-bold">
								{(
									(analysis.daily.optimizationSavings.costSaved /
										(analysis.daily.totalCost +
											analysis.daily.optimizationSavings.costSaved)) *
									100
								).toFixed(1)}
								%
							</div>
							<div className="text-xs text-muted-foreground">
								Cost reduction from optimizations
							</div>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Cost Efficiency by Operation */}
			<Card>
				<CardHeader>
					<CardTitle>Operation Efficiency</CardTitle>
					<CardDescription>Cost efficiency and savings by operation type</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="space-y-3">
						{operationCosts.map((operation, index) => {
							const totalCost = operation.value + operation.savings;
							const efficiency =
								totalCost > 0 ? (operation.savings / totalCost) * 100 : 0;

							return (
								<div
									key={operation.name}
									className="flex items-center justify-between p-3 border rounded-lg"
								>
									<div className="flex items-center space-x-3">
										<div
											className="w-3 h-3 rounded-full"
											style={{
												backgroundColor: COLORS[index % COLORS.length],
											}}
										/>
										<span className="font-medium">{operation.name}</span>
									</div>
									<div className="text-right">
										<div className="text-sm font-medium">
											${operation.value.toFixed(4)}
											<span className="text-green-600 ml-2">
												(-${operation.savings.toFixed(4)})
											</span>
										</div>
										<div className="text-xs text-muted-foreground">
											{efficiency.toFixed(1)}% efficiency
										</div>
									</div>
								</div>
							);
						})}
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
