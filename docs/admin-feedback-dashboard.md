# Admin Feedback Dashboard

## Overview

The Admin Feedback Dashboard provides a secure interface for administrators to view and manage all user feedback submitted through the application.

## Access

- **URL**: `/admin/feedback`
- **Authentication**: Password-protected (not added to main navigation for security)
- **Password**: Set via `ADMIN_DASHBOARD_PASSWORD` environment variable

## Features

### Authentication

- Secure password-based access
- Password visibility toggle
- Session-based authentication during browser session

### Dashboard Overview

- **Total Feedbacks**: Shows count of all feedback entries
- **Status Breakdown**:
    - Pending (yellow): New feedback awaiting review
    - Reviewed (blue): Feedback that has been reviewed
    - Resolved (green): Feedback that has been addressed
    - Dismissed (gray): Feedback that was dismissed

### Feedback Management

- **View All Feedback**: Complete list with user information and timestamps
- **Search**: Search through feedback content and user information
- **Filter by Status**: Filter feedback by current status
- **Status Updates**: Change feedback status directly from the dashboard
- **User Information**: View associated user details (username, provider, ID)

### Search and Filtering

- **Text Search**: Search in feedback message content and user information
- **Status Filter**: Filter by pending, reviewed, resolved, or dismissed status
- **Real-time Updates**: Changes reflect immediately without page refresh

## Configuration

### Environment Variables

Add to your `.env` file:

```env
ADMIN_DASHBOARD_PASSWORD=your_secure_password_here
```

### Database Schema

The dashboard uses the existing `Feedback` model with an added `status` field:

```prisma
model Feedback {
  id         String   @id() @default(uuid())
  message    String
  user_id    String
  user       User     @relation(fields: [user_id], references: [id])
  status     String   @default("pending")
  created_at DateTime @default(now())

  @@index([user_id])
}
```

## API Endpoints

### GET `/api/admin/feedback`

- **Purpose**: Retrieve all feedback with user information
- **Authentication**: Requires `x-admin-password` header
- **Response**: Array of feedback objects with user details

### PATCH `/api/admin/feedback`

- **Purpose**: Update feedback status
- **Authentication**: Requires `x-admin-password` header
- **Body**: `{ id: string, status: string }`
- **Response**: Updated feedback object

## Security Considerations

1. **Password Protection**: Dashboard is protected by environment variable password
2. **No Menu Integration**: Not added to main navigation to prevent accidental discovery
3. **Header Authentication**: API uses custom header for authentication
4. **Session-based**: Authentication persists only during browser session

## Usage Instructions

1. **Access Dashboard**:
    - Navigate to `/admin/feedback`
    - Enter the admin password (from `ADMIN_DASHBOARD_PASSWORD` env var)
    - Click "Login"

2. **View Feedback**:
    - All feedback is displayed in chronological order (newest first)
    - Each card shows user info, timestamp, status, and full message

3. **Search and Filter**:
    - Use the search box to find specific feedback
    - Use the status dropdown to filter by status
    - Combine search and filter for precise results

4. **Manage Status**:
    - Click the status dropdown on any feedback card
    - Select new status (pending, reviewed, resolved, dismissed)
    - Changes save automatically

5. **Refresh Data**:
    - Click the "Refresh" button to reload latest feedback
    - Useful if multiple admins are managing feedback

## Status Workflow

Recommended status workflow:

1. **Pending**: New feedback (default status)
2. **Reviewed**: Admin has read and acknowledged the feedback
3. **Resolved**: Action has been taken based on the feedback
4. **Dismissed**: Feedback was not actionable or relevant

## Troubleshooting

### Cannot Access Dashboard

- Verify `ADMIN_DASHBOARD_PASSWORD` is set in `.env`
- Check that the password matches exactly (case-sensitive)
- Ensure the application is running and database is connected

### No Feedback Showing

- Check if users have submitted feedback through the main application
- Verify database connection and that feedback table exists
- Use the "Refresh" button to reload data

### Status Updates Not Working

- Ensure you're authenticated (password accepted)
- Check browser console for any JavaScript errors
- Verify API endpoints are accessible

## Development Notes

- Dashboard uses client-side rendering for real-time updates
- Built with Next.js App Router and Tailwind CSS
- Uses shadcn/ui components for consistent styling
- Prisma ORM for database operations
- TypeScript for type safety
