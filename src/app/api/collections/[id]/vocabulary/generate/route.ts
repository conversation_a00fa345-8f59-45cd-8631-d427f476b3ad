import { generateRandomWordsPaginatedApi } from '@/backend/api';
import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { auth } from '@/lib/auth';
import { Language } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
	try {
		const { id: collectionId } = await params;
		const session = await auth();

		if (!session?.user?.id) {
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const body = await request.json();
		const {
			keywords,
			maxTerms = 20,
			excludeTerms = [],
			sourceLanguage,
			targetLanguage,
			offset = 0,
		} = body;

		// Validate required fields
		if (!keywords || !Array.isArray(keywords) || keywords.length === 0) {
			return NextResponse.json(
				{ error: 'Keywords array is required and cannot be empty' },
				{ status: 400 }
			);
		}

		if (!sourceLanguage || !targetLanguage) {
			return NextResponse.json(
				{ error: 'Source and target languages are required' },
				{ status: 400 }
			);
		}

		// Validate languages are valid enum values
		if (
			!Object.values(Language).includes(sourceLanguage) ||
			!Object.values(Language).includes(targetLanguage)
		) {
			return NextResponse.json({ error: 'Invalid language values' }, { status: 400 });
		}

		// Validate that source and target languages are different
		if (sourceLanguage === targetLanguage) {
			return NextResponse.json(
				{ error: 'Source and target languages must be different' },
				{ status: 400 }
			);
		}

		// Validate maxTerms range
		if (maxTerms < 1 || maxTerms > 50) {
			return NextResponse.json(
				{ error: 'maxTerms must be between 1 and 50' },
				{ status: 400 }
			);
		}

		// Use the new paginated API function
		const result = await generateRandomWordsPaginatedApi(
			keywords,
			maxTerms,
			[collectionId],
			excludeTerms,
			sourceLanguage,
			targetLanguage,
			offset
		);

		return NextResponse.json(result);
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error('Vocabulary generation error:', error);
		return NextResponse.json({ error: 'Failed to generate vocabulary words' }, { status: 500 });
	}
}
