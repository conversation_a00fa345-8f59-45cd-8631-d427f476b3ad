'use server';

import { NotFoundError, UnauthorizedError, ValidationError } from '@/backend/errors';
import { getLastSeenWordService, getWordService } from '@/backend/wire';
import { auth } from '@/lib';
import { WordDetail } from '@/models';
import { Language } from '@prisma/client';
import { trackWordReviewApi } from './collection-stats.api';

/**
 * Retrieves words in a specific collection for the authenticated user.
 * @param collectionId - The ID of the collection.
 * @returns A promise that resolves to an array of WordDetail objects.
 * @throws {UnauthorizedError} If the user is not authenticated.
 * @throws {ValidationError} If collectionId is missing.
 * @throws {NotFoundError} If the collection is not found for the user.
 * @throws {Error} If fetching words fails for other reasons.
 */
export async function getWordsByCollectionApi(collectionId: string): Promise<WordDetail[]> {
	const session = await auth();
	const userId = session?.user?.id;

	if (!userId) {
		throw new UnauthorizedError(
			'Unauthorized: User must be authenticated to fetch words by collection.'
		);
	}
	if (!collectionId) {
		throw new ValidationError('Collection ID is required to fetch words.');
	}

	const wordService = getWordService();
	try {
		const words = await wordService.getWordsByCollection(userId, collectionId);
		return words as WordDetail[]; // Assuming service returns WordDetail[] or compatible Word[]
	} catch (error) {
		if (
			error instanceof UnauthorizedError ||
			error instanceof ValidationError ||
			error instanceof NotFoundError
		) {
			throw error;
		}
		console.error(
			`Failed to fetch words for collection ${collectionId}, user ${userId}:`,
			error
		);
		throw new Error('Failed to fetch words by collection. Please try again.');
	}
}

/**
 * Saves a word as the "last seen" for the authenticated user.
 * @param wordId - The ID of the word.
 * @param collectionId - Optional collection ID to track stats.
 * @returns A promise that resolves when the operation is complete.
 * @throws {UnauthorizedError} If the user is not authenticated.
 * @throws {ValidationError} If wordId is missing.
 * @throws {NotFoundError} If the specified word does not exist (if service throws this).
 * @throws {Error} If saving fails for other reasons.
 */
export async function createLastSeenWordApi(wordId: string, collectionId?: string): Promise<void> {
	if (!wordId) {
		throw new ValidationError('Word ID is required to save as last seen.');
	}
	if (wordId.trim() === '') {
		throw new ValidationError('Word ID cannot be empty or whitespace.');
	}

	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) {
		throw new UnauthorizedError(
			'Unauthorized: User must be authenticated to save last seen word.'
		);
	}

	const lastSeenWordService = getLastSeenWordService();
	try {
		await lastSeenWordService.saveLastSeenWord(userId, wordId);

		// Track stats if collectionId is provided
		if (collectionId) {
			await trackWordReviewApi(collectionId, userId, 1);
		}
	} catch (error) {
		if (
			error instanceof ValidationError ||
			error instanceof UnauthorizedError ||
			error instanceof NotFoundError
		) {
			throw error;
		}
		console.error(
			`Failed to create last seen word entry for word ${wordId}, user ${userId}:`,
			error
		);
		throw new Error('Failed to save last seen word. Please try again.');
	}
}

/**
 * Retrieves words to review for the authenticated user from a specific collection.
 * @param collectionId - The ID of the collection.
 * @param limit - Optional limit for the number of words (positive number).
 * @param offset - Optional offset for pagination (non-negative number).
 * @returns A promise that resolves to an array of WordDetail objects.
 * @throws {UnauthorizedError} If the user is not authenticated.
 * @throws {ValidationError} If collectionId is missing or limit/offset are invalid.
 * @throws {NotFoundError} If the collection is not found for the user.
 * @throws {Error} If fetching words fails for other reasons.
 */
export async function getWordsToReviewApi(
	collectionId: string,
	limit?: number
): Promise<WordDetail[]> {
	const session = await auth();
	const userId = session?.user?.id;

	if (!userId) {
		throw new UnauthorizedError(
			'Unauthorized: User must be authenticated to get words for review.'
		);
	}
	if (!collectionId) {
		throw new ValidationError('Collection ID is required to get words for review.');
	}
	if (limit !== undefined && (typeof limit !== 'number' || limit < 1)) {
		throw new ValidationError('Limit must be a positive number if provided.');
	}

	const wordService = getWordService();
	try {
		// Assuming wordService.getWordsToReview can handle an undefined offset if not applicable by default
		const words = await wordService.getWordsToReview(userId, collectionId, limit);
		return words as WordDetail[]; // Assuming service returns WordDetail[] or compatible Word[]
	} catch (error) {
		if (
			error instanceof UnauthorizedError ||
			error instanceof ValidationError ||
			error instanceof NotFoundError
		) {
			throw error;
		}
		console.error(
			`Failed to fetch words to review for collection ${collectionId}, user ${userId}:`,
			error
		);
		throw new Error('Failed to fetch words to review. Please try again.');
	}
}

/**
 * Searches for words within a specific collection based on a term and optional language.
 * Requires authentication.
 * @param collectionId - The ID of the collection to search within.
 * @param term - The search term.
 * @param language - Optional language to filter by.
 * @param limit - Optional limit for the number of results (default 10, max 100).
 * @returns A promise that resolves to an array of WordDetail objects.
 * @throws {UnauthorizedError} If the user is not authenticated.
 * @throws {ValidationError} If collectionId or term is missing/empty, or limit is out of range, or language is invalid.
 * @throws {NotFoundError} If the collection is not found for the user.
 * @throws {Error} If search fails for other reasons.
 */
export async function searchWordsApi(
	collectionId: string,
	term: string,
	language?: Language,
	limit: number = 10
): Promise<WordDetail[]> {
	const session = await auth();
	const userId = session?.user?.id;

	if (!userId) {
		throw new UnauthorizedError(
			'Unauthorized: User must be authenticated to search words in a collection.'
		);
	}

	if (!collectionId || typeof collectionId !== 'string' || collectionId.trim() === '') {
		throw new ValidationError('Collection ID is required and must be a non-empty string.');
	}

	term = term.trim();
	// It's okay for the term to be empty for this search, the service layer will handle it by returning all words in the collection (filtered by language if provided)
	// if (!term) {
	// 	throw new ValidationError('Search term is required.');
	// }

	if (typeof limit !== 'number' || limit < 1 || limit > 100) {
		throw new ValidationError('Limit must be a number between 1 and 100.');
	}
	if (language && !Object.values(Language).includes(language)) {
		throw new ValidationError(`Invalid language provided: ${language}`);
	}

	const wordService = getWordService();
	try {
		const words = await wordService.searchWordsInCollection(
			userId,
			collectionId,
			term,
			language,
			limit
		);
		return words as WordDetail[];
	} catch (error) {
		if (
			error instanceof UnauthorizedError ||
			error instanceof ValidationError ||
			error instanceof NotFoundError
		) {
			throw error;
		}
		console.error(
			`Failed to search words in collection ${collectionId} for term "${term}", language ${language}, user ${userId}:`,
			error
		);
		throw new Error('Failed to search words in collection. Please try again.');
	}
}

/**
 * Retrieves multiple words by their IDs.
 * This API does not require authentication as per its original design.
 * @param wordIds - An array of word IDs to fetch (max 50).
 * @returns A promise that resolves to an array of WordDetail objects.
 * @throws {ValidationError} If wordIds array is missing, empty, or exceeds max length.
 * @throws {Error} If fetching fails for other reasons.
 */
export async function getWordsByIdsApi(wordIds: string[]): Promise<WordDetail[]> {
	if (!Array.isArray(wordIds) || wordIds.length === 0) {
		throw new ValidationError('Word IDs array is required and cannot be empty.');
	}
	if (wordIds.length > 50) {
		throw new ValidationError('Cannot fetch more than 50 words by IDs at a time.');
	}
	if (wordIds.some((id) => typeof id !== 'string' || id.trim() === '')) {
		throw new ValidationError('Each word ID in wordIds must be a non-empty string.');
	}

	const wordService = getWordService();
	try {
		const words = await wordService.findWordsByIds(wordIds);
		return words as WordDetail[]; // Assuming service returns WordDetail[] or compatible Word[]
	} catch (error) {
		if (error instanceof ValidationError) {
			throw error;
		}
		// Consider if service could throw NotFoundError if *all* IDs are invalid.
		// If only some are invalid, it typically returns the ones found.
		console.error(`Failed to fetch words by IDs: [${wordIds.join(', ')}]:`, error);
		throw new Error('Failed to fetch words by IDs. Please try again.');
	}
}

/**
 * Bulk deletes words from a specific collection for the authenticated user.
 * @param collectionId - The ID of the collection.
 * @param wordIds - An array of word IDs to delete (max 100).
 * @returns A promise that resolves to the number of words deleted.
 * @throws {UnauthorizedError} If the user is not authenticated.
 * @throws {ValidationError} If collectionId is missing or wordIds array is invalid.
 * @throws {NotFoundError} If the collection is not found for the user.
 * @throws {Error} If deleting words fails for other reasons.
 */
export async function bulkDeleteWordsFromCollectionApi(
	collectionId: string,
	wordIds: string[]
): Promise<number> {
	const session = await auth();
	const userId = session?.user?.id;

	if (!userId) {
		throw new UnauthorizedError(
			'Unauthorized: User must be authenticated to delete words from collection.'
		);
	}
	if (!collectionId || typeof collectionId !== 'string' || collectionId.trim() === '') {
		throw new ValidationError('Collection ID is required and must be a non-empty string.');
	}
	if (!Array.isArray(wordIds) || wordIds.length === 0) {
		throw new ValidationError('Word IDs array is required and cannot be empty.');
	}
	if (wordIds.length > 100) {
		throw new ValidationError('Cannot delete more than 100 words at a time.');
	}
	if (wordIds.some((id) => typeof id !== 'string' || id.trim() === '')) {
		throw new ValidationError('Each word ID in wordIds must be a non-empty string.');
	}

	const wordService = getWordService();
	try {
		const deletedCount = await wordService.bulkDeleteWordsFromCollection(
			userId,
			collectionId,
			wordIds
		);
		return deletedCount;
	} catch (error) {
		if (
			error instanceof UnauthorizedError ||
			error instanceof ValidationError ||
			error instanceof NotFoundError
		) {
			throw error;
		}
		console.error(
			`Failed to bulk delete words from collection ${collectionId}, user ${userId}:`,
			error
		);
		throw new Error('Failed to delete words from collection. Please try again.');
	}
}
