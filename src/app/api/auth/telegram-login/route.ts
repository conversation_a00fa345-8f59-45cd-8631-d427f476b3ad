import { provider<PERSON>ogin<PERSON><PERSON> } from '@/backend/api';
import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { Provider } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const { provider_id } = body;

		if (!provider_id) {
			return NextResponse.json({ error: 'Provider ID is required' }, { status: 400 });
		}

		await provider<PERSON>ogin<PERSON><PERSON>(Provider.TELEGRAM, provider_id);

		return NextResponse.json({
			success: true,
			message: 'Telegram login successful',
		});
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error('Telegram login error:', error);
		return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
	}
}
