'use client';

import { <PERSON>ert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useTokenMonitoring } from '@/lib/api/token-monitor.api';
import {
	Activity,
	AlertTriangle,
	BarChart3,
	Brain,
	CheckCircle,
	Database,
	DollarSign,
	Layers,
	RefreshCw,
	TrendingDown,
	TrendingUp,
	Zap,
} from 'lucide-react';
import { BatchProcessingStats } from './batch-processing-stats';
import { CachePerformance } from './cache-performance';
import { CostTrendChart } from './cost-trend-chart';
import { ModelSelectionStats } from './model-selection-stats';
import { OptimizationMetrics } from './optimization-metrics';
import { TokenUsageChart } from './token-usage-chart';

export function TokenMonitoringDashboard() {
	const {
		stats,
		analysis,
		suggestions,
		alerts,
		cacheStats,
		batchStats,
		modelStats,
		loading,
		error,
		refresh,
	} = useTokenMonitoring(30000); // Refresh every 30 seconds

	if (loading && !stats) {
		return (
			<div className="flex items-center justify-center h-64">
				<LoadingSpinner size="lg" />
				<span className="ml-2">Loading monitoring data...</span>
			</div>
		);
	}

	if (error) {
		return (
			<Alert variant="destructive">
				<AlertTriangle className="h-4 w-4" />
				<AlertTitle>Error Loading Dashboard</AlertTitle>
				<AlertDescription>{error}</AlertDescription>
			</Alert>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">Token Monitoring Dashboard</h1>
					<p className="text-muted-foreground">
						Real-time insights into OpenAI API usage and optimization performance
					</p>
				</div>
				<Button onClick={refresh} variant="outline" size="sm">
					<RefreshCw className="h-4 w-4 mr-2" />
					Refresh
				</Button>
			</div>

			{/* Alerts */}
			{alerts && alerts.length > 0 && (
				<div className="space-y-2">
					{alerts.map((alert, index) => (
						<Alert
							key={index}
							variant={alert.severity === 'critical' ? 'destructive' : 'default'}
						>
							<AlertTriangle className="h-4 w-4" />
							<AlertTitle>{alert.type.replace('_', ' ').toUpperCase()}</AlertTitle>
							<AlertDescription>{alert.message}</AlertDescription>
						</Alert>
					))}
				</div>
			)}

			{/* Key Metrics */}
			{stats && (
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">
								Total Tokens Today
							</CardTitle>
							<Zap className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{stats.totalTokens.toLocaleString()}
							</div>
							<p className="text-xs text-muted-foreground">
								{stats.averageTokensPerRequest.toFixed(0)} avg per request
							</p>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">Total Cost Today</CardTitle>
							<DollarSign className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">${stats.totalCost.toFixed(4)}</div>
							<p className="text-xs text-muted-foreground">
								${stats.averageCostPerRequest.toFixed(6)} avg per request
							</p>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">Tokens Saved</CardTitle>
							<TrendingDown className="h-4 w-4 text-green-600" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold text-green-600">
								{stats.optimizationSavings.tokensSaved.toLocaleString()}
							</div>
							<p className="text-xs text-muted-foreground">
								{((1 - stats.optimizationSavings.compressionRatio) * 100).toFixed(
									1
								)}
								% reduction
							</p>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">Cost Saved</CardTitle>
							<DollarSign className="h-4 w-4 text-green-600" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold text-green-600">
								${stats.optimizationSavings.costSaved.toFixed(4)}
							</div>
							<p className="text-xs text-muted-foreground">
								From optimization techniques
							</p>
						</CardContent>
					</Card>
				</div>
			)}

			{/* Main Dashboard Tabs */}
			<Tabs defaultValue="overview" className="space-y-4">
				<TabsList className="grid w-full grid-cols-6">
					<TabsTrigger value="overview">
						<BarChart3 className="h-4 w-4 mr-2" />
						Overview
					</TabsTrigger>
					<TabsTrigger value="optimization">
						<TrendingUp className="h-4 w-4 mr-2" />
						Optimization
					</TabsTrigger>
					<TabsTrigger value="cache">
						<Database className="h-4 w-4 mr-2" />
						Cache
					</TabsTrigger>
					<TabsTrigger value="batch">
						<Layers className="h-4 w-4 mr-2" />
						Batch
					</TabsTrigger>
					<TabsTrigger value="models">
						<Brain className="h-4 w-4 mr-2" />
						Models
					</TabsTrigger>
					<TabsTrigger value="suggestions">
						<CheckCircle className="h-4 w-4 mr-2" />
						Suggestions
					</TabsTrigger>
				</TabsList>

				<TabsContent value="overview" className="space-y-4">
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
						{analysis && <TokenUsageChart analysis={analysis} />}
						{analysis && <CostTrendChart analysis={analysis} />}
					</div>
					{analysis && <OptimizationMetrics analysis={analysis} />}
				</TabsContent>

				<TabsContent value="optimization" className="space-y-4">
					{analysis && (
						<div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
							<Card>
								<CardHeader>
									<CardTitle className="flex items-center">
										<TrendingDown className="h-5 w-5 mr-2 text-green-600" />
										Token Usage Trend
									</CardTitle>
								</CardHeader>
								<CardContent>
									<div className="text-3xl font-bold">
										{analysis.trends.tokenUsageTrend > 0 ? '+' : ''}
										{analysis.trends.tokenUsageTrend.toFixed(1)}%
									</div>
									<p className="text-sm text-muted-foreground">
										vs previous period
									</p>
								</CardContent>
							</Card>

							<Card>
								<CardHeader>
									<CardTitle className="flex items-center">
										<DollarSign className="h-5 w-5 mr-2 text-blue-600" />
										Cost Trend
									</CardTitle>
								</CardHeader>
								<CardContent>
									<div className="text-3xl font-bold">
										{analysis.trends.costTrend > 0 ? '+' : ''}
										{analysis.trends.costTrend.toFixed(1)}%
									</div>
									<p className="text-sm text-muted-foreground">
										vs previous period
									</p>
								</CardContent>
							</Card>

							<Card>
								<CardHeader>
									<CardTitle className="flex items-center">
										<Activity className="h-5 w-5 mr-2 text-purple-600" />
										Optimization Trend
									</CardTitle>
								</CardHeader>
								<CardContent>
									<div className="text-3xl font-bold">
										{analysis.trends.optimizationTrend > 0 ? '+' : ''}
										{analysis.trends.optimizationTrend.toFixed(1)}%
									</div>
									<p className="text-sm text-muted-foreground">
										compression improvement
									</p>
								</CardContent>
							</Card>
						</div>
					)}
				</TabsContent>

				<TabsContent value="cache" className="space-y-4">
					{cacheStats && <CachePerformance cacheStats={cacheStats} />}
				</TabsContent>

				<TabsContent value="batch" className="space-y-4">
					{batchStats && <BatchProcessingStats batchStats={batchStats} />}
				</TabsContent>

				<TabsContent value="models" className="space-y-4">
					{modelStats && <ModelSelectionStats modelStats={modelStats} />}
				</TabsContent>

				<TabsContent value="suggestions" className="space-y-4">
					{suggestions && suggestions.length > 0 ? (
						<div className="space-y-4">
							{suggestions.map((suggestion, index) => (
								<Card key={index}>
									<CardHeader>
										<div className="flex items-center justify-between">
											<CardTitle className="flex items-center">
												<Badge
													variant={
														suggestion.priority === 'high'
															? 'destructive'
															: suggestion.priority === 'medium'
																? 'default'
																: 'secondary'
													}
													className="mr-2"
												>
													{suggestion.priority}
												</Badge>
												{suggestion.type.toUpperCase()} -{' '}
												{suggestion.operation}
											</CardTitle>
											<div className="text-right">
												<div className="text-sm font-medium text-green-600">
													{suggestion.estimatedSavings.percentage}%
													savings
												</div>
												<div className="text-xs text-muted-foreground">
													${suggestion.estimatedSavings.cost.toFixed(4)}
												</div>
											</div>
										</div>
									</CardHeader>
									<CardContent>
										<p className="text-sm text-muted-foreground mb-2">
											{suggestion.description}
										</p>
										<p className="text-sm font-medium">
											Implementation: {suggestion.implementation}
										</p>
									</CardContent>
								</Card>
							))}
						</div>
					) : (
						<Card>
							<CardContent className="text-center py-8">
								<CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
								<h3 className="text-lg font-medium">No Optimization Suggestions</h3>
								<p className="text-muted-foreground">
									Your system is running optimally! Check back later for new
									suggestions.
								</p>
							</CardContent>
						</Card>
					)}
				</TabsContent>
			</Tabs>
		</div>
	);
}
