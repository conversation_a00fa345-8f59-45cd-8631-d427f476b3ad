'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { CacheStats, TokenMonitorAPI } from '@/lib/api/token-monitor.api';
import { Clock, Database, Hash, RefreshCw, Trash2, TrendingUp } from 'lucide-react';
import React from 'react';
import { toast } from 'sonner';

interface CachePerformanceProps {
	cacheStats: CacheStats;
}

export function CachePerformance({ cacheStats }: CachePerformanceProps) {
	const [isCleaningUp, setIsCleaningUp] = React.useState(false);

	const handleCleanup = async () => {
		setIsCleaningUp(true);
		try {
			const result = await TokenMonitorAPI.cleanupCache();
			toast.success(`Cache cleanup completed: ${result.message}`);
		} catch (error) {
			toast.error('Failed to cleanup cache');
			console.error('Cache cleanup error:', error);
		} finally {
			setIsCleaningUp(false);
		}
	};

	const hitRateColor =
		cacheStats.regular.hitRate >= 0.8
			? 'text-green-600'
			: cacheStats.regular.hitRate >= 0.6
				? 'text-yellow-600'
				: 'text-red-600';

	const hitRateVariant =
		cacheStats.regular.hitRate >= 0.8
			? 'default'
			: cacheStats.regular.hitRate >= 0.6
				? 'secondary'
				: 'destructive';

	return (
		<div className="space-y-4">
			{/* Cache Overview */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Hit Rate</CardTitle>
						<TrendingUp className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className={`text-2xl font-bold ${hitRateColor}`}>
							{(cacheStats.regular.hitRate * 100).toFixed(1)}%
						</div>
						<p className="text-xs text-muted-foreground">
							{cacheStats.regular.hits} hits /{' '}
							{cacheStats.regular.hits + cacheStats.regular.misses} total
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Cache Entries</CardTitle>
						<Database className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{cacheStats.combined.totalEntries}</div>
						<p className="text-xs text-muted-foreground">
							{cacheStats.combined.expiredEntries} expired
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
						<RefreshCw className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{(cacheStats.regular.memoryUsage / 1024 / 1024).toFixed(1)}MB
						</div>
						<p className="text-xs text-muted-foreground">
							{cacheStats.regular.keys} keys stored
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Keyword Index</CardTitle>
						<Hash className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{cacheStats.combined.keywordIndex}</div>
						<p className="text-xs text-muted-foreground">Semantic keywords indexed</p>
					</CardContent>
				</Card>
			</div>

			{/* Cache Performance Details */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center">
							<Database className="h-5 w-5 mr-2" />
							Regular Cache Performance
						</CardTitle>
						<CardDescription>Standard cache hit/miss statistics</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="space-y-2">
							<div className="flex justify-between items-center">
								<span className="text-sm font-medium">Hit Rate</span>
								<Badge variant={hitRateVariant}>
									{(cacheStats.regular.hitRate * 100).toFixed(1)}%
								</Badge>
							</div>
							<Progress value={cacheStats.regular.hitRate * 100} className="h-2" />
						</div>

						<div className="grid grid-cols-2 gap-4">
							<div className="text-center p-3 bg-green-50 dark:bg-green-950 rounded-lg">
								<div className="text-2xl font-bold text-green-600">
									{cacheStats.regular.hits}
								</div>
								<div className="text-sm text-green-600">Cache Hits</div>
							</div>
							<div className="text-center p-3 bg-red-50 dark:bg-red-950 rounded-lg">
								<div className="text-2xl font-bold text-red-600">
									{cacheStats.regular.misses}
								</div>
								<div className="text-sm text-red-600">Cache Misses</div>
							</div>
						</div>

						<div className="space-y-2">
							<div className="flex justify-between">
								<span className="text-sm">Total Keys:</span>
								<span className="font-medium">{cacheStats.regular.keys}</span>
							</div>
							<div className="flex justify-between">
								<span className="text-sm">Memory Usage:</span>
								<span className="font-medium">
									{(cacheStats.regular.memoryUsage / 1024 / 1024).toFixed(2)} MB
								</span>
							</div>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle className="flex items-center">
							<Hash className="h-5 w-5 mr-2" />
							Semantic Cache Performance
						</CardTitle>
						<CardDescription>Advanced semantic similarity matching</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="flex items-center justify-between">
							<span className="text-sm font-medium">Status</span>
							<Badge variant={cacheStats.semantic.enabled ? 'default' : 'secondary'}>
								{cacheStats.semantic.enabled ? 'Enabled' : 'Disabled'}
							</Badge>
						</div>

						{cacheStats.semantic.enabled && (
							<>
								<div className="grid grid-cols-2 gap-4">
									<div className="text-center p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
										<div className="text-2xl font-bold text-blue-600">
											{cacheStats.semantic.totalEntries}
										</div>
										<div className="text-sm text-blue-600">
											Semantic Entries
										</div>
									</div>
									<div className="text-center p-3 bg-purple-50 dark:bg-purple-950 rounded-lg">
										<div className="text-2xl font-bold text-purple-600">
											{cacheStats.semantic.totalKeywords}
										</div>
										<div className="text-sm text-purple-600">
											Keywords Indexed
										</div>
									</div>
								</div>

								<div className="space-y-2">
									<div className="flex justify-between">
										<span className="text-sm">Average Access:</span>
										<span className="font-medium">
											{cacheStats.semantic.averageAccess.toFixed(1)}
										</span>
									</div>
									<div className="flex justify-between">
										<span className="text-sm">Expired Entries:</span>
										<span className="font-medium">
											{cacheStats.semantic.expiredCount}
										</span>
									</div>
								</div>
							</>
						)}

						{!cacheStats.semantic.enabled && (
							<div className="text-center py-4 text-muted-foreground">
								<Hash className="h-8 w-8 mx-auto mb-2 opacity-50" />
								<p className="text-sm">Semantic caching is disabled</p>
								<p className="text-xs">
									Enable in configuration to use similarity matching
								</p>
							</div>
						)}
					</CardContent>
				</Card>
			</div>

			{/* Cache Management */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center">
						<Trash2 className="h-5 w-5 mr-2" />
						Cache Management
					</CardTitle>
					<CardDescription>
						Manage cache entries and perform maintenance operations
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="flex items-center justify-between">
						<div>
							<h4 className="font-medium">Cleanup Expired Entries</h4>
							<p className="text-sm text-muted-foreground">
								Remove {cacheStats.combined.expiredEntries} expired cache entries to
								free up memory
							</p>
						</div>
						<Button
							onClick={handleCleanup}
							disabled={isCleaningUp || cacheStats.combined.expiredEntries === 0}
							variant="outline"
						>
							{isCleaningUp ? (
								<RefreshCw className="h-4 w-4 mr-2 animate-spin" />
							) : (
								<Trash2 className="h-4 w-4 mr-2" />
							)}
							{isCleaningUp ? 'Cleaning...' : 'Cleanup Cache'}
						</Button>
					</div>
				</CardContent>
			</Card>

			{/* Cache Efficiency Recommendations */}
			<Card>
				<CardHeader>
					<CardTitle>Performance Recommendations</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-3">
						{cacheStats.regular.hitRate < 0.6 && (
							<div className="flex items-start space-x-3 p-3 bg-yellow-50 dark:bg-yellow-950 rounded-lg">
								<Clock className="h-5 w-5 text-yellow-600 mt-0.5" />
								<div>
									<h4 className="font-medium text-yellow-800 dark:text-yellow-200">
										Low Hit Rate Detected
									</h4>
									<p className="text-sm text-yellow-700 dark:text-yellow-300">
										Consider increasing cache TTL or enabling semantic caching
										to improve hit rates.
									</p>
								</div>
							</div>
						)}

						{cacheStats.combined.expiredEntries > 50 && (
							<div className="flex items-start space-x-3 p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
								<Trash2 className="h-5 w-5 text-blue-600 mt-0.5" />
								<div>
									<h4 className="font-medium text-blue-800 dark:text-blue-200">
										Cache Cleanup Recommended
									</h4>
									<p className="text-sm text-blue-700 dark:text-blue-300">
										{cacheStats.combined.expiredEntries} expired entries
										detected. Run cleanup to optimize memory usage.
									</p>
								</div>
							</div>
						)}

						{!cacheStats.semantic.enabled && (
							<div className="flex items-start space-x-3 p-3 bg-purple-50 dark:bg-purple-950 rounded-lg">
								<Hash className="h-5 w-5 text-purple-600 mt-0.5" />
								<div>
									<h4 className="font-medium text-purple-800 dark:text-purple-200">
										Enable Semantic Caching
									</h4>
									<p className="text-sm text-purple-700 dark:text-purple-300">
										Semantic caching can improve hit rates by matching similar
										requests. Enable in configuration for better performance.
									</p>
								</div>
							</div>
						)}

						{cacheStats.regular.hitRate >= 0.8 && cacheStats.semantic.enabled && (
							<div className="flex items-start space-x-3 p-3 bg-green-50 dark:bg-green-950 rounded-lg">
								<TrendingUp className="h-5 w-5 text-green-600 mt-0.5" />
								<div>
									<h4 className="font-medium text-green-800 dark:text-green-200">
										Excellent Cache Performance
									</h4>
									<p className="text-sm text-green-700 dark:text-green-300">
										Your cache is performing optimally with high hit rates and
										semantic matching enabled.
									</p>
								</div>
							</div>
						)}
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
